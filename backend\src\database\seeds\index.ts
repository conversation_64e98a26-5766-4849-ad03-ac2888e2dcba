import { DataSource } from 'typeorm';
import { seedLearningPaths } from './learning-paths.seed';
import { seedLearningGoals } from './learning-goals.seed';

export async function runSeeds(dataSource: DataSource) {
  console.log('Starting database seeding...');
  
  try {
    // Seed learning paths first (goals may reference paths)
    console.log('Seeding learning paths...');
    await seedLearningPaths(dataSource);
    
    // Seed learning goals
    console.log('Seeding learning goals...');
    await seedLearningGoals(dataSource);
    
    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error during database seeding:', error);
    throw error;
  }
}
