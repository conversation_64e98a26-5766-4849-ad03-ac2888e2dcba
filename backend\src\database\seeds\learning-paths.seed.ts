import { DataSource } from 'typeorm';
import { LearningPath, LearningPathType, LearningPathCategory, LearningPathStatus, LearningPathDifficulty } from '../../entities/learning-path.entity';
import { LearningPathMilestone } from '../../entities/learning-path-milestone.entity';
import { User } from '../../entities/user.entity';

export async function seedLearningPaths(dataSource: DataSource) {
  const learningPathRepository = dataSource.getRepository(LearningPath);
  const milestoneRepository = dataSource.getRepository(LearningPathMilestone);
  const userRepository = dataSource.getRepository(User);

  // Find or create a system user for creating paths
  let systemUser = await userRepository.findOne({ where: { email: '<EMAIL>' } });
  if (!systemUser) {
    systemUser = userRepository.create({
      email: '<EMAIL>',
      first_name: 'System',
      last_name: 'Administrator',
      password: 'system123', // This would be hashed in real implementation
      role: 'admin',
      is_verified: true,
    });
    await userRepository.save(systemUser);
  }

  // USMLE Step 1 Preparation Path
  const usmleStep1Path = learningPathRepository.create({
    title: 'USMLE Step 1 Mastery Path',
    description: 'Comprehensive preparation for USMLE Step 1 exam covering all major medical disciplines with integrated practice questions and clinical correlations.',
    type: LearningPathType.TEMPLATE,
    category: LearningPathCategory.USMLE_STEP1,
    difficulty: LearningPathDifficulty.INTERMEDIATE,
    status: LearningPathStatus.PUBLISHED,
    estimated_duration_weeks: 16,
    estimated_hours_per_week: 25,
    tags: ['usmle', 'step1', 'medical-boards', 'comprehensive'],
    learning_objectives: [
      'Master fundamental medical sciences concepts',
      'Develop clinical reasoning skills',
      'Achieve target USMLE Step 1 score',
      'Build confidence in exam-taking strategies',
    ],
    prerequisites: {
      completed_courses: ['basic-sciences-foundation'],
      minimum_gpa: 3.0,
      recommended_experience: 'Completion of pre-clinical curriculum',
    },
    path_structure: {
      phases: [
        {
          id: 'phase_1',
          title: 'Foundation Review',
          description: 'Review and strengthen fundamental medical sciences',
          order: 1,
          estimated_duration_weeks: 4,
          modules: [
            {
              id: 'module_1_1',
              title: 'Anatomy & Physiology Review',
              description: 'Comprehensive review of human anatomy and physiology',
              type: 'course',
              resource_id: 'anatomy-physiology-review',
              estimated_duration_hours: 40,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_1_2',
              title: 'Biochemistry Fundamentals',
              description: 'Essential biochemistry concepts for USMLE',
              type: 'course',
              resource_id: 'biochemistry-fundamentals',
              estimated_duration_hours: 30,
              is_required: true,
              order: 2,
            },
            {
              id: 'module_1_3',
              title: 'Foundation Assessment',
              description: 'Diagnostic assessment to identify knowledge gaps',
              type: 'assessment',
              resource_id: 'foundation-diagnostic',
              estimated_duration_hours: 3,
              is_required: true,
              order: 3,
            },
          ],
        },
        {
          id: 'phase_2',
          title: 'Systems-Based Learning',
          description: 'Deep dive into organ systems with clinical correlations',
          order: 2,
          estimated_duration_weeks: 8,
          modules: [
            {
              id: 'module_2_1',
              title: 'Cardiovascular System',
              description: 'Comprehensive cardiovascular pathophysiology',
              type: 'course',
              resource_id: 'cardiovascular-system',
              estimated_duration_hours: 25,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_2_2',
              title: 'Respiratory System',
              description: 'Pulmonary medicine and pathophysiology',
              type: 'course',
              resource_id: 'respiratory-system',
              estimated_duration_hours: 20,
              is_required: true,
              order: 2,
            },
            {
              id: 'module_2_3',
              title: 'Cardiopulmonary Clinical Cases',
              description: 'Interactive clinical cases for cardiopulmonary systems',
              type: 'clinical_case',
              resource_id: 'cardiopulm-cases',
              estimated_duration_hours: 15,
              is_required: true,
              order: 3,
            },
            {
              id: 'module_2_4',
              title: 'Systems Integration Assessment',
              description: 'Assessment covering integrated systems knowledge',
              type: 'assessment',
              resource_id: 'systems-integration',
              estimated_duration_hours: 4,
              is_required: true,
              order: 4,
            },
          ],
        },
        {
          id: 'phase_3',
          title: 'Practice & Mastery',
          description: 'Intensive practice with USMLE-style questions',
          order: 3,
          estimated_duration_weeks: 4,
          modules: [
            {
              id: 'module_3_1',
              title: 'Question Bank Practice',
              description: 'Systematic practice with high-yield questions',
              type: 'assessment',
              resource_id: 'usmle-qbank',
              estimated_duration_hours: 60,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_3_2',
              title: 'Simulated Exams',
              description: 'Full-length practice examinations',
              type: 'assessment',
              resource_id: 'usmle-simulated',
              estimated_duration_hours: 24,
              is_required: true,
              order: 2,
            },
          ],
        },
      ],
    },
    created_by: systemUser,
    analytics: {
      total_enrollments: 0,
      completion_rate: 0,
      average_completion_time_weeks: 0,
      user_ratings: { average: 0, count: 0 },
      difficulty_feedback: { too_easy: 0, just_right: 0, too_hard: 0 },
    },
  });

  await learningPathRepository.save(usmleStep1Path);

  // Create milestones for USMLE Step 1 path
  const usmleStep1Milestones = [
    {
      title: 'Foundation Mastery',
      description: 'Complete foundation review with 80% or higher scores',
      type: 'phase_completion',
      order: 1,
      is_required: true,
      criteria: {
        type: 'phase_completion',
        conditions: { phase_id: 'phase_1', minimum_score: 80 },
      },
      rewards: { points: 100, badge_id: 'foundation-master' },
      learning_path: usmleStep1Path,
    },
    {
      title: 'Systems Expert',
      description: 'Demonstrate mastery of organ systems integration',
      type: 'assessment_score',
      order: 2,
      is_required: true,
      criteria: {
        type: 'assessment_score',
        conditions: { assessment_id: 'systems-integration', minimum_score: 85 },
      },
      rewards: { points: 150, badge_id: 'systems-expert' },
      learning_path: usmleStep1Path,
    },
    {
      title: 'Practice Champion',
      description: 'Complete 1000+ practice questions with 75% accuracy',
      type: 'custom',
      order: 3,
      is_required: false,
      criteria: {
        type: 'custom',
        conditions: { questions_completed: 1000, accuracy_threshold: 75 },
      },
      rewards: { points: 200, badge_id: 'practice-champion' },
      learning_path: usmleStep1Path,
    },
  ];

  for (const milestoneData of usmleStep1Milestones) {
    const milestone = milestoneRepository.create(milestoneData);
    await milestoneRepository.save(milestone);
  }

  // Clinical Skills Development Path
  const clinicalSkillsPath = learningPathRepository.create({
    title: 'Clinical Skills Mastery',
    description: 'Develop essential clinical skills including physical examination, patient communication, and clinical reasoning through hands-on practice and simulation.',
    type: LearningPathType.TEMPLATE,
    category: LearningPathCategory.CLINICAL_SKILLS,
    difficulty: LearningPathDifficulty.INTERMEDIATE,
    status: LearningPathStatus.PUBLISHED,
    estimated_duration_weeks: 12,
    estimated_hours_per_week: 15,
    tags: ['clinical-skills', 'physical-exam', 'patient-care', 'communication'],
    learning_objectives: [
      'Master physical examination techniques',
      'Develop effective patient communication skills',
      'Build clinical reasoning abilities',
      'Practice documentation and presentation skills',
    ],
    prerequisites: {
      completed_courses: ['basic-clinical-introduction'],
      minimum_progress: 'pre-clinical-completion',
    },
    path_structure: {
      phases: [
        {
          id: 'phase_1',
          title: 'Physical Examination Fundamentals',
          description: 'Master basic physical examination techniques',
          order: 1,
          estimated_duration_weeks: 4,
          modules: [
            {
              id: 'module_1_1',
              title: 'General Physical Exam',
              description: 'Systematic approach to physical examination',
              type: 'course',
              resource_id: 'general-physical-exam',
              estimated_duration_hours: 20,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_1_2',
              title: 'Cardiovascular Examination',
              description: 'Detailed cardiac examination techniques',
              type: 'course',
              resource_id: 'cardiac-exam',
              estimated_duration_hours: 15,
              is_required: true,
              order: 2,
            },
            {
              id: 'module_1_3',
              title: 'Physical Exam Practice Cases',
              description: 'Virtual patient examinations',
              type: 'clinical_case',
              resource_id: 'physical-exam-cases',
              estimated_duration_hours: 25,
              is_required: true,
              order: 3,
            },
          ],
        },
        {
          id: 'phase_2',
          title: 'Patient Communication',
          description: 'Develop effective patient interaction skills',
          order: 2,
          estimated_duration_weeks: 4,
          modules: [
            {
              id: 'module_2_1',
              title: 'Medical Interviewing',
              description: 'Structured approach to patient interviews',
              type: 'course',
              resource_id: 'medical-interviewing',
              estimated_duration_hours: 18,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_2_2',
              title: 'Difficult Conversations',
              description: 'Managing challenging patient interactions',
              type: 'course',
              resource_id: 'difficult-conversations',
              estimated_duration_hours: 12,
              is_required: true,
              order: 2,
            },
            {
              id: 'module_2_3',
              title: 'Communication Simulations',
              description: 'Practice patient communication scenarios',
              type: 'clinical_case',
              resource_id: 'communication-sims',
              estimated_duration_hours: 20,
              is_required: true,
              order: 3,
            },
          ],
        },
        {
          id: 'phase_3',
          title: 'Clinical Reasoning Integration',
          description: 'Integrate skills into clinical decision-making',
          order: 3,
          estimated_duration_weeks: 4,
          modules: [
            {
              id: 'module_3_1',
              title: 'Clinical Reasoning Framework',
              description: 'Systematic approach to clinical thinking',
              type: 'course',
              resource_id: 'clinical-reasoning',
              estimated_duration_hours: 15,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_3_2',
              title: 'Integrated Clinical Cases',
              description: 'Complex patient scenarios requiring full skill integration',
              type: 'clinical_case',
              resource_id: 'integrated-cases',
              estimated_duration_hours: 30,
              is_required: true,
              order: 2,
            },
            {
              id: 'module_3_3',
              title: 'Clinical Skills Assessment',
              description: 'Comprehensive evaluation of clinical skills',
              type: 'assessment',
              resource_id: 'clinical-skills-osce',
              estimated_duration_hours: 4,
              is_required: true,
              order: 3,
            },
          ],
        },
      ],
    },
    created_by: systemUser,
    analytics: {
      total_enrollments: 0,
      completion_rate: 0,
      average_completion_time_weeks: 0,
      user_ratings: { average: 0, count: 0 },
      difficulty_feedback: { too_easy: 0, just_right: 0, too_hard: 0 },
    },
  });

  await learningPathRepository.save(clinicalSkillsPath);

  // Internal Medicine Residency Prep Path
  const internalMedPath = learningPathRepository.create({
    title: 'Internal Medicine Residency Preparation',
    description: 'Comprehensive preparation for internal medicine residency including core knowledge, clinical skills, and professional development.',
    type: LearningPathType.TEMPLATE,
    category: LearningPathCategory.SPECIALTY_PREP,
    difficulty: LearningPathDifficulty.ADVANCED,
    status: LearningPathStatus.PUBLISHED,
    estimated_duration_weeks: 20,
    estimated_hours_per_week: 20,
    tags: ['internal-medicine', 'residency-prep', 'clinical-knowledge', 'professional-development'],
    learning_objectives: [
      'Master internal medicine core knowledge',
      'Develop advanced clinical reasoning skills',
      'Prepare for residency interviews and applications',
      'Build professional competencies',
    ],
    prerequisites: {
      completed_courses: ['clinical-rotations-core'],
      minimum_step_scores: { step1: 220, step2: 230 },
    },
    path_structure: {
      phases: [
        {
          id: 'phase_1',
          title: 'Core Internal Medicine Knowledge',
          description: 'Master essential internal medicine concepts',
          order: 1,
          estimated_duration_weeks: 8,
          modules: [
            {
              id: 'module_1_1',
              title: 'Cardiology Essentials',
              description: 'Core cardiology knowledge for internal medicine',
              type: 'course',
              resource_id: 'im-cardiology',
              estimated_duration_hours: 40,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_1_2',
              title: 'Pulmonology Fundamentals',
              description: 'Essential pulmonary medicine concepts',
              type: 'course',
              resource_id: 'im-pulmonology',
              estimated_duration_hours: 35,
              is_required: true,
              order: 2,
            },
            {
              id: 'module_1_3',
              title: 'Gastroenterology Core',
              description: 'Key gastroenterology topics for internists',
              type: 'course',
              resource_id: 'im-gastroenterology',
              estimated_duration_hours: 30,
              is_required: true,
              order: 3,
            },
            {
              id: 'module_1_4',
              title: 'Internal Medicine Board Review',
              description: 'Comprehensive board-style questions',
              type: 'assessment',
              resource_id: 'im-board-review',
              estimated_duration_hours: 20,
              is_required: true,
              order: 4,
            },
          ],
        },
        {
          id: 'phase_2',
          title: 'Advanced Clinical Skills',
          description: 'Develop advanced clinical competencies',
          order: 2,
          estimated_duration_weeks: 8,
          modules: [
            {
              id: 'module_2_1',
              title: 'Complex Case Management',
              description: 'Managing complex internal medicine patients',
              type: 'clinical_case',
              resource_id: 'complex-im-cases',
              estimated_duration_hours: 50,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_2_2',
              title: 'Procedures and Interventions',
              description: 'Common internal medicine procedures',
              type: 'course',
              resource_id: 'im-procedures',
              estimated_duration_hours: 25,
              is_required: true,
              order: 2,
            },
            {
              id: 'module_2_3',
              title: 'Quality Improvement',
              description: 'Healthcare quality and patient safety',
              type: 'course',
              resource_id: 'quality-improvement',
              estimated_duration_hours: 15,
              is_required: true,
              order: 3,
            },
          ],
        },
        {
          id: 'phase_3',
          title: 'Professional Development',
          description: 'Prepare for residency and career development',
          order: 3,
          estimated_duration_weeks: 4,
          modules: [
            {
              id: 'module_3_1',
              title: 'Residency Application Strategy',
              description: 'Optimize your residency application',
              type: 'course',
              resource_id: 'residency-application',
              estimated_duration_hours: 10,
              is_required: true,
              order: 1,
            },
            {
              id: 'module_3_2',
              title: 'Interview Preparation',
              description: 'Master residency interviews',
              type: 'course',
              resource_id: 'interview-prep',
              estimated_duration_hours: 8,
              is_required: true,
              order: 2,
            },
            {
              id: 'module_3_3',
              title: 'Professional Skills Assessment',
              description: 'Comprehensive professional competency evaluation',
              type: 'assessment',
              resource_id: 'professional-skills',
              estimated_duration_hours: 3,
              is_required: true,
              order: 3,
            },
          ],
        },
      ],
    },
    created_by: systemUser,
    analytics: {
      total_enrollments: 0,
      completion_rate: 0,
      average_completion_time_weeks: 0,
      user_ratings: { average: 0, count: 0 },
      difficulty_feedback: { too_easy: 0, just_right: 0, too_hard: 0 },
    },
  });

  await learningPathRepository.save(internalMedPath);

  console.log('Learning paths seeded successfully!');
}
